'use client'

import React, { useEffect } from 'react'
import { CrudFormCompact } from '../crud/crud-form-compact'
import { <PERSON>rudField } from '../crud/types'

interface FormSection {
  title: string
  fields: string[]
}

interface FormLayout {
  type: 'compact' | 'standard'
  columns: number
  sections: FormSection[]
}

interface TeamMemberModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
  title: string
  initialData?: any
  fields: CrudField[]
  layout?: FormLayout
}

export function TeamMemberModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  title, 
  initialData, 
  fields,
  layout 
}: TeamMemberModalProps) {
  
  if (!isOpen) return null

  return (
    <CrudFormCompact
      fields={fields}
      initialData={initialData}
      onSubmit={onSubmit}
      onCancel={onClose}
      title={title}
      submitLabel={initialData ? 'Update' : 'Create'}
      layout={layout}
    />
  )
}

import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  withError<PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import bcrypt from 'bcryptjs'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/users/[id] - Get a specific user
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const user = await prisma.users.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      emailverified: true,
      firstname: true,
      lastname: true,
      imageurl: true,
      role: true,
      createdat: true,
      updatedat: true,
      blogPosts: {
        select: {
          id: true,
          title: true,
          ispublished: true,
          createdat: true
        }
      },
      contactForms: {
        select: {
          id: true,
          subject: true,
          status: true,
          createdat: true
        }
      },
      _count: {
        select: {
          blogPosts: true,
          contactForms: true,
          messages: true
        }
      }
    }
  })

  if (!user) {
    throw new ApiError('User not found', 404)
  }

  return successResponse(user)
})

// PUT /api/admin/users/[id] - Update a user
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.user.update)
  const data = await validate(request)

  // Check if user exists
  const existingUser = await prisma.users.findUnique({
    where: { id },
  })

  if (!existingUser) {
    throw new ApiError('User not found', 404)
  }

  // Check if email is being changed and if it conflicts with another user
  if (data.email && data.email !== existingUser.email) {
    const emailConflict = await prisma.users.findFirst({
      where: {
        email: data.email,
        id: { not: id }
      },
    })

    if (emailConflict) {
      throw new ApiError('A user with this email already exists', 400)
    }
  }

  // Hash password if provided
  let updateData = { ...data }
  if (data.password) {
    updateData.password = await bcrypt.hash(data.password, 12)
  }

  const user = await prisma.users.update({
    where: { id },
    data: updateData,
    select: {
      id: true,
      email: true,
      emailverified: true,
      firstname: true,
      lastname: true,
      imageurl: true,
      role: true,
      createdat: true,
      updatedat: true
    }
  })

  return successResponse(user, 'User updated successfully')
})

// DELETE /api/admin/users/[id] - Delete a user
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if user has associated data that should be preserved
  const userWithData = await prisma.users.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          blogPosts: true,
          contactForms: true,
          messages: true
        }
      }
    }
  })

  if (!userWithData) {
    throw new ApiError('User not found', 404)
  }

  // If user has important data, you might want to prevent deletion
  // or implement a soft delete instead
  if (userWithData._count.blogPosts > 0) {
    throw new ApiError('Cannot delete user with published blog posts', 400)
  }

  await prisma.users.delete({
    where: { id }
  })

  return successResponse(null, 'User deleted successfully')
})

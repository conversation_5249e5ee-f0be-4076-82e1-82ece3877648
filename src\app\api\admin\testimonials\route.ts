import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  withError<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/testimonials - Get all testimonials with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['clientname', 'clienttitle', 'clientcompany', 'content'])
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get testimonials with pagination
  const [testimonials, total] = await Promise.all([
    prisma.testimonialss.findMany({
      where: searchQuery,
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.testimonialss.count({ where: searchQuery })
  ])

  return paginatedResponse(testimonials, page, limit, total)
})

// POST /api/admin/testimonials - Create a new testimonial
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.testimonial.create)
  const data = await validate(request)

  const testimonial = await prisma.testimonialss.create({
    data
  })

  return successResponse(testimonial, 'Testimonial created successfully', 201)
})

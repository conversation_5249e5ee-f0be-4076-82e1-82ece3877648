import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  withError<PERSON>and<PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  generateSlug
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/legal-pages - Get all legal pages with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['title', 'slug', 'content'])
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get legal pages with pagination
  const [legalPages, total] = await Promise.all([
    prisma.legalpages.findMany({
      where: searchQuery,
      include: {
        sections: {
          orderBy: { displayorder: 'asc' }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.legalpages.count({ where: searchQuery })
  ])

  return paginatedResponse(legalPages, page, limit, total)
})

// POST /api/admin/legal-pages - Create a new legal page
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.legalPage.create)
  const data = await validate(request)

  // Generate slug if not provided
  if (!data.slug) {
    data.slug = generateSlug(data.title)
  }

  const legalPage = await prisma.legalpages.create({
    data,
    include: {
      sections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  return successResponse(legalPage, 'Legal page created successfully', 201)
})

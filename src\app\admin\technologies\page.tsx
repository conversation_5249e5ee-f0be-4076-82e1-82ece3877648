'use client';

import CrudManager from '@/components/admin/crud/crud-manager';

const technologyConfig = {
  title: 'Technologies',
  description: 'Manage your technology stack, skills, and expertise levels',
  endpoint: 'technologies',
  fields: [
    {
      key: 'name',
      label: 'Technology Name',
      type: 'text',
      required: true,
      searchable: true,
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      required: true,
      searchable: true,
    },
    {
      key: 'iconUrl',
      label: 'Icon URL',
      type: 'url',
      searchable: false,
    },
    {
      key: 'displayOrder',
      label: 'Display Order',
      type: 'number',
      defaultValue: 0,
      searchable: false,
    },
    {
      key: 'isActive',
      label: 'Active',
      type: 'boolean',
      defaultValue: true,
      searchable: false,
    },
  ],
  displayFields: ['name', 'description', 'iconUrl', 'displayOrder', 'isActive'],
  searchFields: ['name', 'description'],
  sortableFields: ['name', 'displayOrder', 'createdAt'],
  filterFields: [
    {
      key: 'isActive',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' },
      ],
    },
  ],
  actions: {
    create: true,
    edit: true,
    delete: true,
    bulkDelete: true,
    export: true,
  },
};

export default function TechnologiesPage() {
  return <CrudManager config={technologyConfig} />;
}

import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: { id: string }
}

// GET /api/admin/contact-forms/[id] - Get a specific contact form
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const contactForm = await prisma.contactforms.findUnique({
    where: { id: params.id },
    include: {
      user: {
        select: {
          id: true,
          firstname: true,
          lastname: true,
          email: true,
          role: true
        }
      }
    }
  })

  if (!contactForm) {
    throw new ApiError('Contact form not found', 404)
  }

  return successResponse(contactForm)
})

// PUT /api/admin/contact-forms/[id] - Update a contact form (mainly for status updates)
export const PUT = withError<PERSON>andler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.contactForm.update)
  const data = await validate(request)

  const contactForm = await prisma.contactforms.update({
    where: { id: params.id },
    data,
    include: {
      user: {
        select: {
          id: true,
          firstname: true,
          lastname: true,
          email: true
        }
      }
    }
  })

  return successResponse(contactForm, 'Contact form updated successfully')
})

// DELETE /api/admin/contact-forms/[id] - Delete a contact form
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  await prisma.contactforms.delete({
    where: { id: params.id }
  })

  return successResponse(null, 'Contact form deleted successfully')
})

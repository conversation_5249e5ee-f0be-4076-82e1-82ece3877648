import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/jobs/[id] - Get a specific job listing
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const jobListing = await prisma.joblistings.findUnique({
    where: { id },
    include: {
      applications: {
        orderBy: { appliedAt: 'desc' }
      }
    }
  })

  if (!jobListing) {
    throw new ApiError('Job listing not found', 404)
  }

  return successResponse(jobListing)
})

// PUT /api/admin/jobs/[id] - Update a job listing
export const PUT = with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.jobListing.update)
  const data = await validate(request)

  // Check if job listing exists
  const existingJob = await prisma.joblistings.findUnique({
    where: { id },
  })

  if (!existingJob) {
    throw new ApiError('Job listing not found', 404)
  }

  const jobListing = await prisma.joblistings.update({
    where: { id },
    data,
    include: {
      applications: {
        select: {
          id: true,
          applicantName: true,
          applicantEmail: true,
          status: true,
          appliedAt: true
        }
      }
    }
  })

  return successResponse(jobListing, 'Job listing updated successfully')
})

// DELETE /api/admin/jobs/[id] - Delete a job listing
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if job listing exists
  const existingJob = await prisma.joblistings.findUnique({
    where: { id },
  })

  if (!existingJob) {
    throw new ApiError('Job listing not found', 404)
  }

  // Delete all applications first
  await prisma.jobapplications.deleteMany({
    where: { jobListingId: id }
  })

  // Then delete the job listing
  await prisma.joblistings.delete({
    where: { id }
  })

  return successResponse(null, 'Job listing deleted successfully')
})

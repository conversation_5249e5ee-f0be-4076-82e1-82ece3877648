'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { CrudField } from './types'
import { LoadingSpinner } from '../ui/loading-spinner'
import { SimpleScrollSimulator } from '../ui/simple-scroll-simulator'
import { SimpleFileUpload } from '../ui/simple-file-upload'
import styles from '../ui/scroll-simulator.module.css'

interface FormSection {
  title: string
  fields: string[]
}

interface FormLayout {
  type: 'compact' | 'standard'
  columns: number
  sections: FormSection[]
}

interface CrudFormCompactProps {
  fields: CrudField[]
  initialData?: any
  onSubmit: (data: any) => Promise<void>
  onCancel: () => void
  title: string
  submitLabel?: string
  isLoading?: boolean
  layout?: FormLayout
}

export function CrudFormCompact({
  fields,
  initialData,
  onSubmit,
  onCancel,
  title,
  submitLabel = 'Save',
  isLoading = false,
  layout
}: CrudFormCompactProps) {
  const [formData, setFormData] = useState<any>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  // Initialize form data
  useEffect(() => {
    const initialFormData: any = {}
    fields.forEach(field => {
      const fieldKey = field.key || field.name
      if (initialData && initialData[fieldKey] !== undefined) {
        initialFormData[fieldKey] = initialData[fieldKey]
      } else if (field.defaultValue !== undefined) {
        initialFormData[fieldKey] = field.defaultValue
      } else {
        // Set default values based on field type
        switch (field.type) {
          case 'boolean':
          case 'checkbox':
            initialFormData[fieldKey] = false
            break
          case 'number':
            initialFormData[fieldKey] = ''
            break
          default:
            initialFormData[fieldKey] = ''
        }
      }
    })
    setFormData(initialFormData)
  }, [fields, initialData])



  const validateField = (field: CrudField, value: any): string | null => {
    // Required validation
    if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      return `${field.label} is required`
    }

    // Skip other validations if field is empty and not required
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return null
    }

    // Type-specific validations
    if (field.type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        return 'Please enter a valid email address'
      }
    }

    if (field.type === 'url' && value) {
      const fieldKey = field.key || field.name
      const isFileUpload = fieldKey === 'photoUrl' || fieldKey === 'resumeUrl'

      if (isFileUpload) {
        // For file upload fields, accept local paths starting with / or full URLs
        const isLocalPath = value.startsWith('/')
        const isValidUrl = (() => {
          try {
            new URL(value)
            return true
          } catch {
            return false
          }
        })()

        if (!isLocalPath && !isValidUrl) {
          return 'Please enter a valid file path or URL'
        }
      } else {
        // For regular URL fields, require full URLs
        try {
          new URL(value)
        } catch {
          return 'Please enter a valid URL'
        }
      }
    }

    return null
  }

  const handleInputChange = (field: CrudField, value: any) => {
    const fieldKey = field.key || field.name
    setFormData((prev: any) => ({ ...prev, [fieldKey]: value }))
    
    // Clear error when user starts typing
    if (errors[fieldKey]) {
      setErrors(prev => ({ ...prev, [fieldKey]: '' }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate all fields
    const newErrors: Record<string, string> = {}
    fields.forEach(field => {
      const fieldKey = field.key || field.name
      const error = validateField(field, formData[fieldKey])
      if (error) {
        newErrors[fieldKey] = error
      }
    })

    setErrors(newErrors)

    if (Object.keys(newErrors).length > 0) {
      return
    }

    try {
      setIsSubmitting(true)

      // Clean up form data before submission
      const cleanedFormData = { ...formData }

      // Convert empty strings and null values to undefined for optional fields
      Object.keys(cleanedFormData).forEach(key => {
        if (cleanedFormData[key] === '' || cleanedFormData[key] === null) {
          cleanedFormData[key] = undefined
        }
      })

      // Remove undefined values to avoid sending them
      const finalFormData = Object.fromEntries(
        Object.entries(cleanedFormData).filter(([_, value]) => value !== undefined)
      )

      console.log('Submitting cleaned form data:', finalFormData)
      await onSubmit(finalFormData)
    } catch (error) {
      console.error('Form submission error:', error)
      throw error
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderField = (field: CrudField, isCompact = false) => {
    const fieldKey = field.key || field.name
    const value = formData[fieldKey] || ''
    const error = errors[fieldKey]
    const isDisabled = field.disabled || isSubmitting

    const baseInputClasses = `w-full px-3 ${isCompact ? 'py-2' : 'py-2.5'} border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm ${
      error
        ? 'border-red-300 bg-red-50 focus:ring-red-200'
        : 'border-gray-300 focus:border-blue-500 hover:border-gray-400'
    } ${isDisabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`

    const labelClasses = `block text-sm font-semibold text-gray-700 ${isCompact ? 'mb-1.5' : 'mb-2'}`

    switch (field.type) {
      case 'textarea':
        return (
          <div key={fieldKey} className="space-y-1.5">
            <label className={labelClasses}>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <textarea
              value={value}
              onChange={(e) => handleInputChange(field, e.target.value || undefined)}
              placeholder={field.placeholder}
              disabled={isDisabled}
              rows={field.rows || (isCompact ? 3 : 4)}
              className={`${baseInputClasses} resize-none`}
            />
            {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
          </div>
        )

      case 'select':
        return (
          <div key={fieldKey} className="space-y-1">
            <label className={labelClasses}>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <select
              value={value}
              onChange={(e) => handleInputChange(field, e.target.value || undefined)}
              disabled={isDisabled}
              className={baseInputClasses}
            >
              {field.options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {error && <p className="text-xs text-red-600">{error}</p>}
          </div>
        )

      case 'boolean':
        return (
          <div key={fieldKey} className="space-y-1.5">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors">
              <input
                type="checkbox"
                checked={value}
                onChange={(e) => handleInputChange(field, e.target.checked)}
                disabled={isDisabled}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors"
              />
              <label className="text-sm font-semibold text-gray-700 cursor-pointer">
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>
            </div>
            {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
          </div>
        )

      case 'date':
        return (
          <div key={fieldKey} className="space-y-1">
            <label className={labelClasses}>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type="date"
              value={value ? (typeof value === 'string' ? value.split('T')[0] : new Date(value).toISOString().split('T')[0]) : ''}
              onChange={(e) => handleInputChange(field, e.target.value || undefined)}
              disabled={isDisabled}
              className={baseInputClasses}
            />
            {error && <p className="text-xs text-red-600">{error}</p>}
          </div>
        )

      case 'url':
        // Check if this is a file upload field based on field key
        const isFileUpload = fieldKey === 'photoUrl' || fieldKey === 'resumeUrl'
        const uploadType = fieldKey === 'photoUrl' ? 'image' : 'document'

        if (isFileUpload) {
          return (
            <div key={fieldKey} className="space-y-1">
              <label className={labelClasses}>
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>
              <SimpleFileUpload
                value={value}
                onChange={(path) => handleInputChange(field, path)}
                uploadType={uploadType}
                placeholder={field.placeholder}
                disabled={isDisabled}
                className="w-full"
              />
              {error && <p className="text-xs text-red-600">{error}</p>}
            </div>
          )
        }

        return (
          <div key={fieldKey} className="space-y-1">
            <label className={labelClasses}>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type="url"
              value={value}
              onChange={(e) => handleInputChange(field, e.target.value || undefined)}
              placeholder={field.placeholder}
              disabled={isDisabled}
              className={baseInputClasses}
            />
            {error && <p className="text-xs text-red-600">{error}</p>}
          </div>
        )

      case 'number':
        return (
          <div key={fieldKey} className="space-y-1">
            <label className={labelClasses}>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type="number"
              value={value}
              onChange={(e) => handleInputChange(field, e.target.value || undefined)}
              placeholder={field.placeholder}
              disabled={isDisabled}
              className={baseInputClasses}
              min={field.min}
              max={field.max}
              step={field.step}
            />
            {error && <p className="text-xs text-red-600">{error}</p>}
          </div>
        )

      default:
        return (
          <div key={fieldKey} className="space-y-1.5">
            <label className={labelClasses}>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type={field.type}
              value={value}
              onChange={(e) => handleInputChange(field, e.target.value || undefined)}
              placeholder={field.placeholder}
              disabled={isDisabled}
              className={baseInputClasses}
            />
            {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
          </div>
        )
    }
  }

  const renderSectionedForm = () => {
    if (!layout?.sections) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {fields.map(field => renderField(field, true))}
        </div>
      )
    }

    return (
      <div className="space-y-6">
        {layout.sections.map((section, sectionIndex) => {
          const sectionFields = fields.filter(field =>
            section.fields.includes(field.key || field.name)
          )

          if (sectionFields.length === 0) return null

          return (
            <div key={sectionIndex} className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
              {/* Section Header */}
              <div className="px-5 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-xl">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <span className="bg-blue-600 text-white text-xs font-bold px-2.5 py-1 rounded-full mr-3 min-w-[24px] text-center">
                    {sectionIndex + 1}
                  </span>
                  {section.title}
                </h3>
              </div>

              {/* Section Content */}
              <div className="p-5">
                <div className={`grid gap-4 ${
                  layout.columns === 2 ? 'grid-cols-1 sm:grid-cols-2' :
                  layout.columns === 3 ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' :
                  layout.columns === 4 ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' :
                  'grid-cols-1'
                }`}>
                  {sectionFields.map(field => renderField(field, true))}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-lg shadow-xl max-w-7xl w-full h-[95vh] overflow-hidden flex flex-col"
      >
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
            <p className="text-sm text-gray-600 mt-1">Fill in the information below to {initialData ? 'update' : 'create'} the team member</p>
          </div>
          <button
            onClick={onCancel}
            disabled={isSubmitting}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:cursor-not-allowed rounded-full hover:bg-white"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="flex flex-col h-full">
          {/* Scrollable Content */}
          <div
            className={`flex-1 relative ${styles.scrollContainer}`}
            ref={scrollContainerRef}
            style={{
              maxHeight: 'calc(95vh - 200px)',
              minHeight: '400px',
              overflowY: 'auto'
            }}
          >
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              <div className={`p-6 ${styles.scrollContent}`}>
                {renderSectionedForm()}
                {/* Add extra content to ensure scrolling is possible */}
                <div style={{ height: '100px' }}></div>
              </div>
            )}
          </div>

          {/* Simple Scroll Simulator */}
          <SimpleScrollSimulator
            containerRef={scrollContainerRef}
            forceShow={false}
            debug={false}
          />

          {/* Sticky Footer */}
          <div className="flex items-center justify-end space-x-3 px-6 py-4 border-t border-gray-200 bg-gray-50 sticky bottom-0">
            <button
              type="button"
              onClick={onCancel}
              disabled={isSubmitting}
              className="px-6 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:cursor-not-allowed font-medium"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 font-medium"
            >
              {isSubmitting && <LoadingSpinner size="sm" />}
              <span>{submitLabel}</span>
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  withError<PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/technologies/[id] - Get a specific technology
export const GET = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  await requireAdmin(request)

  const technology = await prisma.technologies.findUnique({
    where: {
      id: Number(params.id),
    },
    include: {
      projecttechnologies: {
        include: {
          projects: {
            select: {
              id: true,
              name: true,
              status: true,
              projstartdate: true,
              projcompletiondate: true,
              clients: {
                select: {
                  id: true,
                  companyname: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdat: 'desc',
        },
      },
      _count: {
        select: {
          projecttechnologies: true,
        },
      },
    },
  })

  if (!technology) {
    return NextResponse.json(
      { success: false, error: 'Technology not found' },
      { status: 404 }
    )
  }

  const transformedTechnology = transformFromDbFields.technology(technology)
  return successResponse(transformedTechnology, 'Technology retrieved successfully')
})

// PUT /api/admin/technologies/[id] - Update a specific technology
export const PUT = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.technology?.update || schemas.category.update)
  const data = await validate(request)

  // Check if technology exists
  const existingTechnology = await prisma.technologies.findUnique({
    where: { id: Number(params.id) },
  })

  if (!existingTechnology) {
    return NextResponse.json(
      { success: false, error: 'Technology not found' },
      { status: 404 }
    )
  }

  // Check if name is being changed and if it conflicts with another technology
  if (data.name && data.name !== existingTechnology.name) {
    const nameConflict = await prisma.technologies.findFirst({
      where: {
        name: data.name,
        id: { not: Number(params.id) },
      },
    })

    if (nameConflict) {
      throw new Error('A technology with this name already exists')
    }
  }

  const updatedTechnology = await prisma.technologies.update({
    where: {
      id: Number(params.id),
    },
    data: {
      ...transformToDbFields.technology(data),
      updatedat: new Date(),
    },
    include: {
      _count: {
        select: {
          projecttechnologies: true,
        },
      },
    },
  })

  const transformedTechnology = transformFromDbFields.technology(updatedTechnology)
  return successResponse(transformedTechnology, 'Technology updated successfully')
})

// DELETE /api/admin/technologies/[id] - Delete a specific technology
export const DELETE = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  await requireAdmin(request)

  // Check if technology exists
  const existingTechnology = await prisma.technologies.findUnique({
    where: { id: Number(params.id) },
    include: {
      _count: {
        select: {
          projecttechnologies: true,
        },
      },
    },
  })

  if (!existingTechnology) {
    return NextResponse.json(
      { success: false, error: 'Technology not found' },
      { status: 404 }
    )
  }

  // Check if technology has associated projects
  if (existingTechnology._count.projecttechnologies > 0) {
    return NextResponse.json(
      { 
        success: false, 
        error: `Cannot delete technology ${existingTechnology.name}. It is associated with ${existingTechnology._count.projecttechnologies} project(s). Please remove it from projects first.` 
      },
      { status: 400 }
    )
  }

  await prisma.technologies.delete({
    where: {
      id: Number(params.id),
    },
  })

  return successResponse(
    { id: Number(params.id) },
    'Technology deleted successfully'
  )
})

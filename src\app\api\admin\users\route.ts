import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import bcrypt from 'bcryptjs'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/users - Get all users with pagination and search
export const GET = withError<PERSON>and<PERSON>(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['email', 'firstname', 'lastname'])
  
  // Add role filter if provided
  if (filter) {
    searchQuery.role = filter
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get users with pagination (exclude password field)
  const [users, total] = await Promise.all([
    prisma.users.findMany({
      where: searchQuery,
      select: {
        id: true,
        email: true,
        emailverified: true,
        firstname: true,
        lastname: true,
        imageurl: true,
        role: true,
        createdat: true,
        updatedat: true,
        _count: {
          select: {
            blogPosts: true,
            contactForms: true,
            messages: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.users.count({ where: searchQuery })
  ])

  return paginatedResponse(users, page, limit, total)
})

// POST /api/admin/users - Create a new user
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.user.create)
  const data = await validate(request)

  // Check if user with this email already exists
  const existingUser = await prisma.users.findUnique({
    where: { email: data.email }
  })

  if (existingUser) {
    throw new Error('A user with this email already exists')
  }

  // Hash password if provided
  let hashedPassword = undefined
  if (data.password) {
    hashedPassword = await bcrypt.hash(data.password, 12)
  }

  const user = await prisma.users.create({
    data: {
      ...data,
      password: hashedPassword
    },
    select: {
      id: true,
      email: true,
      emailverified: true,
      firstname: true,
      lastname: true,
      imageurl: true,
      role: true,
      createdat: true,
      updatedat: true
    }
  })

  return successResponse(user, 'User created successfully', 201)
})

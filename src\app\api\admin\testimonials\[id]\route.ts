import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/testimonials/[id] - Get a specific testimonial
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const testimonial = await prisma.testimonials.findUnique({
    where: { id }
  })

  if (!testimonial) {
    throw new ApiError('Testimonial not found', 404)
  }

  return successResponse(testimonial)
})

// PUT /api/admin/testimonials/[id] - Update a testimonial
export const PUT = with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.testimonial.update)
  const data = await validate(request)

  // Check if testimonial exists
  const existingTestimonial = await prisma.testimonials.findUnique({
    where: { id },
  })

  if (!existingTestimonial) {
    throw new ApiError('Testimonial not found', 404)
  }

  const testimonial = await prisma.testimonials.update({
    where: { id },
    data
  })

  return successResponse(testimonial, 'Testimonial updated successfully')
})

// DELETE /api/admin/testimonials/[id] - Delete a testimonial
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if testimonial exists
  const existingTestimonial = await prisma.testimonials.findUnique({
    where: { id },
  })

  if (!existingTestimonial) {
    throw new ApiError('Testimonial not found', 404)
  }

  await prisma.testimonials.delete({
    where: { id }
  })

  return successResponse(null, 'Testimonial deleted successfully')
})

import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError,
  generateSlug
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/legal-pages/[id] - Get a specific legal page
export const GET = withError<PERSON>andler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const legalPage = await prisma.legalpages.findUnique({
    where: { id },
    include: {
      sections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  if (!legalPage) {
    throw new ApiError('Legal page not found', 404)
  }

  return successResponse(legalPage)
})

// PUT /api/admin/legal-pages/[id] - Update a legal page
export const PUT = with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.legalPage.update)
  const data = await validate(request)

  // Generate slug if title is updated but slug is not provided
  if (data.title && !data.slug) {
    data.slug = generateSlug(data.title)
  }

  const legalPage = await prisma.legalpages.update({
    where: { id },
    data,
    include: {
      sections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  return successResponse(legalPage, 'Legal page updated successfully')
})

// DELETE /api/admin/legal-pages/[id] - Delete a legal page
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // First delete all associated sections
  await prisma.legalpagesSection.deleteMany({
    where: { legalPageId: id }
  })

  // Then delete the legal page
  await prisma.legalpages.delete({
    where: { id }
  })

  return successResponse(null, 'Legal page deleted successfully')
})

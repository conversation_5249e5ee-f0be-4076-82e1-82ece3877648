import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/contact-forms - Get all contact forms with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['name', 'email', 'company', 'subject', 'message'])
  
  // Add status filter if provided
  if (filter) {
    searchQuery.status = filter
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get contact forms with pagination
  const [contactForms, total] = await Promise.all([
    prisma.contactforms.findMany({
      where: searchQuery,
      include: {
        user: {
          select: {
            id: true,
            firstname: true,
            lastname: true,
            email: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.contactforms.count({ where: searchQuery })
  ])

  return paginatedResponse(contactForms, page, limit, total)
})

// POST /api/admin/contact-forms - Create a new contact form (usually from public form)
export const POST = withErrorHandler(async (request: NextRequest) => {
  // Note: This endpoint might not require admin auth if it's used by public contact form
  // For admin creation, we'll keep the auth requirement
  await requireAdmin(request)

  const validate = validateRequest(schemas.contactForm.create)
  const data = await validate(request)

  const contactForm = await prisma.contactforms.create({
    data,
    include: {
      user: {
        select: {
          id: true,
          firstname: true,
          lastname: true,
          email: true
        }
      }
    }
  })

  return successResponse(contactForm, 'Contact form created successfully', 201)
})
